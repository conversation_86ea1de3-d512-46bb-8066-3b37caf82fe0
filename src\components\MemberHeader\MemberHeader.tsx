import { NavLink, useNavigate } from "react-router-dom";
import { useState, useEffect, useCallback } from "react";
import { useProfile } from "@/hooks/useProfile";
import { useUserProfile } from "@/hooks/useUserProfile";
import { useSDK } from "@/hooks/useSDK";
import {
  CategoriesIcon,
  DashboardIcon,
  DeliveryApplicationsIcon,
  DisputesAndRefundsIcon,
  ListingsIcon,
  PlatformSettingsIcon,
  TransactionsIcon,
} from "@/assets/svgs/AdminDashboard";
import MyAccountIcon from "@/assets/svgs/AdminDashboard/MyAccountIcon";
import { DropdownIcon, InboxIcon } from "@/assets/images";

// Custom hook for unread message count
const useUnreadMessageCount = () => {
  const { sdk } = useSDK();
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const fetchUnreadCount = useCallback(async () => {
    try {
      setLoading(true);
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/conversations/unread-count",
        method: "GET",
      });

      if (!response.error) {
        setUnreadCount(Number(response.data?.unread_count || 0));
      }
    } catch (error) {
      console.error("Error loading unread count:", error);
    } finally {
      setLoading(false);
    }
  }, [sdk]);

  useEffect(() => {
    fetchUnreadCount();

    // Refresh unread count every 30 seconds
    const interval = setInterval(fetchUnreadCount, 30000);

    return () => clearInterval(interval);
  }, [fetchUnreadCount]);

  return { unreadCount, loading, refetch: fetchUnreadCount };
};

const MemberHeader = () => {
  const { profile } = useProfile();
  const { isApprovedDeliveryAgent, deliveryAgentStatus } = useUserProfile();
  const { unreadCount } = useUnreadMessageCount();
  const navigate = useNavigate();
  const [amount, setAmount] = useState("50");
  const [fromCurrency, setFromCurrency] = useState("eBa$");
  const [toCurrency, setToCurrency] = useState("USD");
  const [deliveryDropdownOpen, setDeliveryDropdownOpen] = useState(false);

  // Mock conversion rates - in real app, this would come from admin settings
  const conversionRates = {
    USD: 1.45, // 1 eBa$ = 1.45 USD
    EUR: 1.35, // 1 eBa$ = 1.35 EUR
    GBP: 1.15, // 1 eBa$ = 1.15 GBP
    JPY: 160.0, // 1 eBa$ = 160 JPY
    CAD: 1.95, // 1 eBa$ = 1.95 CAD
    AUD: 2.2, // 1 eBa$ = 2.20 AUD
  };

  const convertedAmount =
    fromCurrency === "eBa$" && toCurrency in conversionRates
      ? (
          parseFloat(amount) *
          conversionRates[toCurrency as keyof typeof conversionRates]
        ).toFixed(2)
      : fromCurrency in conversionRates && toCurrency === "eBa$"
        ? (
            parseFloat(amount) /
            conversionRates[fromCurrency as keyof typeof conversionRates]
          ).toFixed(2)
        : parseFloat(amount).toFixed(2);

  // Check if user is approved as delivery agent
  const isDeliveryAgent = isApprovedDeliveryAgent;

  const menuItems = [
    { name: "Dashboard", path: "/member/dashboard", icon: <DashboardIcon /> },
    {
      name: "Marketplace",
      path: "/member/marketplace",
      icon: <ListingsIcon />,
    },
    { name: "My Listings", path: "/member/listings", icon: <ListingsIcon /> },
    {
      name: "My Transactions",
      path: "/member/transactions",
      icon: <TransactionsIcon />,
    },
    {
      name: "My Rewards",
      path: "/member/rewards",
      icon: <DisputesAndRefundsIcon />,
    },

    { name: "My Account", path: "/member/account", icon: <MyAccountIcon /> },
  ];

  const deliveryMenuItems = [
    {
      name: "My Deliveries",
      path: "/member/deliveries",
      icon: <DeliveryApplicationsIcon />,
    },
    {
      name: "Available Deliveries",
      path: "/member/available-deliveries",
      icon: <CategoriesIcon />,
    },
    {
      name: "Delivery Settings",
      path: "/member/delivery-settings",
      icon: <PlatformSettingsIcon />,
    },
  ];

  return (
    <div className="flex h-screen w-[250px] flex-col bg-[#0F2C59] text-white border-r border-gray-600">
      <div className="p-4 border-b border-gray-600 flex-shrink-0">
        {/* eBaDollar Logo */}
        <div className="flex items-center">
          <h1 className="text-2xl font-bold">
            <span style={{ color: "#E63946" }}>eBa</span>
            <span className="text-white">Dollar</span>
          </h1>
        </div>
      </div>
      <nav className="flex flex-col p-4 overflow-y-auto flex-1">
        {menuItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            className={({ isActive }) =>
              `mb-2 rounded-md p-2 flex items-center ${
                isActive ? "bg-[#E63946]" : "hover:bg-gray-700"
              }`
            }
          >
            <span className="mr-2">{item.icon}</span>
            {item.name}
          </NavLink>
        ))}

        {/* Delivery Agent Section */}
        {isDeliveryAgent && (
          <div className="mt-4 pt-4 border-t border-gray-600">
            {/* Delivery Dropdown Header */}
            <button
              onClick={() => setDeliveryDropdownOpen(!deliveryDropdownOpen)}
              className="mb-2 w-full flex items-center justify-between rounded-md p-2 text-left hover:bg-gray-700"
            >
              <div className="flex items-center">
                <span className="mr-2">🚚</span>
                <span>eBa Delivery</span>
              </div>
              <span
                className={`transform transition-transform ${deliveryDropdownOpen ? "rotate-180" : ""}`}
              >
                ▼
              </span>
            </button>

            {/* Delivery Menu Items */}
            {deliveryDropdownOpen && (
              <div className="ml-4 space-y-1">
                {deliveryMenuItems.map((item) => (
                  <NavLink
                    key={item.name}
                    to={item.path}
                    className={({ isActive }) =>
                      `flex items-center rounded-md p-2 text-sm ${
                        isActive ? "bg-[#E63946]" : "hover:bg-gray-700"
                      }`
                    }
                  >
                    <span className="mr-2">{item.icon}</span>
                    {item.name}
                  </NavLink>
                ))}
              </div>
            )}
          </div>
        )}
      </nav>
      <div className="mt-auto py-4 border-t border-gray-600 flex-shrink-0">
        {/* Currency Converter */}
        <div className="w-full flex items-center justify-center border">
          <div className="mb-4 rounded-lg bg-[#0F2C59] p-3   w-full max-w-[187px] mx-0">
            <h3 className="mb-3 text-sm font-medium text-white text-center">
              Currency Converter
            </h3>

            {/* From Currency Row */}
            <div className="mb-3 ">
              <div className=" flex ">
                <div>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className=" rounded-l-md border border-gray-300 bg-white px-2 py-1 h-[38px] text-sm text-gray-900 placeholder-gray-500 focus:border-blue-400 focus:outline-none w-[120px]"
                    placeholder="0"
                  />
                </div>
                <div className="rounded-r-md border border-l-0 border-gray-300 bg-[#E5E7EB] px-2 py-1 text-sm text-gray-900 flex items-center  justify-center w-[59px]">
                  eBa$
                </div>
              </div>
            </div>

            {/* To Currency Row */}
            <div className="mb-3 flex gap-2">
              <div className="flex-1 flex">
                <div className="flex w-full rounded-md border border-gray-300 bg-white overflow-hidden">
                  <div className="flex-1 px-3 py-2 text-sm text-gray-900 flex items-center">
                    {toCurrency}
                  </div>
                  <div className="w-12 bg-gray-100 flex items-center justify-center border-l border-gray-300 relative">
                    <select
                      value={toCurrency}
                      onChange={(e) => setToCurrency(e.target.value)}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer text-black"
                      style={{ color: "black" }}
                    >
                      <option
                        value="USD"
                        style={{ color: "black", backgroundColor: "white" }}
                      >
                        USD
                      </option>
                      <option
                        value="EUR"
                        style={{ color: "black", backgroundColor: "white" }}
                      >
                        EUR
                      </option>
                      <option
                        value="GBP"
                        style={{ color: "black", backgroundColor: "white" }}
                      >
                        GBP
                      </option>
                      <option
                        value="JPY"
                        style={{ color: "black", backgroundColor: "white" }}
                      >
                        JPY
                      </option>
                      <option
                        value="CAD"
                        style={{ color: "black", backgroundColor: "white" }}
                      >
                        CAD
                      </option>
                      <option
                        value="AUD"
                        style={{ color: "black", backgroundColor: "white" }}
                      >
                        AUD
                      </option>
                    </select>
                    <svg
                      className="w-4 h-4 text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Conversion Result */}
            <div className="flex items-center justify-between text-white text-sm">
              <span>
                eBa${amount} = {convertedAmount} {toCurrency}
              </span>
              <button
                onClick={() => {
                  setFromCurrency(toCurrency);
                  setToCurrency(fromCurrency);
                }}
                className="text-red-500 hover:text-red-400 transition-colors"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Inbox Section */}
        <div className=" border-y border-gray-600 mb-4 py-2 flex items-center">

        <NavLink
          to="/member/inbox"
          className={({ isActive }) =>
            ` flex items-center rounded-md p-2 text-sm text-white ${
              isActive ? "bg-[#E63946]" : "hover:bg-gray-700 px-4 "
            }`
          }
        >
          <img src={InboxIcon} alt="Inbox" className="mr-2 w-4 h-4" />
          <span className="flex-1">Inbox</span>
          {unreadCount > 0 && (
            <span className="ml-auto rounded-full bg-[#E63946] px-2 py-1 text-xs font-medium min-w-[20px] text-center">
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          )}
        </NavLink>
        </div>

        {/* Profile Section */}
        <div
          className="flex items-center pt-2 cursor-pointer hover:bg-gray-700 rounded-md p-2 -m-2 px-4"
          onClick={() => navigate("/member/profile")}
        >
          <div className="mr-3 h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center overflow-hidden">
            {profile?.photo ? (
              <img
                src={profile.photo}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-sm text-white">👤</span>
            )}
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-white">
              {profile?.first_name && profile?.last_name
                ? `${profile.first_name} ${profile.last_name}`
                : profile?.first_name || profile?.email || "User"}
            </div>
          </div>
          <button className="text-gray-400 hover:text-white">
            <img src={DropdownIcon} alt="Dropdown" className="w-4 h-4" />
          </button>
        </div>
      </div>
      {/* Logout Button */}
      <div className="p-4 border-t border-gray-600">
        <button
          onClick={() => {
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            localStorage.removeItem("role");
            localStorage.removeItem("refresh_token");
            navigate("/member/login", { replace: true });
          }}
          className="w-full flex items-center justify-center gap-2 rounded-md bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2"
        >
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2v1" />
          </svg>
          Logout
        </button>
      </div>
    </div>
  );
};

export default MemberHeader;
